/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

h1 {
    text-align: center;
    color: white;
    margin-bottom: 30px;
    font-size: 2.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* Video styles */
#videos {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    justify-content: center;
    flex-wrap: wrap;
}

.video-container {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    background: #000;
}

.video-container label {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
}

video {
    width: 320px;
    height: 240px;
    display: block;
    border-radius: 12px;
}

#localVideo {
    transform: scaleX(-1); /* Mirror effect */
}

/* Button styles */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

#buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

#controls {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

/* Room info */
#roomInfo {
    text-align: center;
    margin-bottom: 20px;
}

#currentRoom {
    display: inline-block;
    background: rgba(255,255,255,0.9);
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    color: #333;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Modal styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    min-width: 300px;
    text-align: center;
}

.modal-content h3 {
    margin-bottom: 20px;
    color: #333;
}

.modal-content label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    text-align: left;
}

.modal-content input {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    margin-bottom: 20px;
    font-size: 16px;
}

.modal-content input:focus {
    outline: none;
    border-color: #007bff;
}

.modal-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
}

/* Chat styles */
#chatContainer {
    background: rgba(255,255,255,0.95);
    border-radius: 12px;
    padding: 20px;
    margin-top: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

#chatHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #eee;
}

#messages {
    height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 15px;
    background: white;
}

.message {
    margin-bottom: 10px;
    padding: 8px 12px;
    border-radius: 18px;
    max-width: 70%;
    word-wrap: break-word;
}

.message.local {
    background: #007bff;
    color: white;
    margin-left: auto;
    text-align: right;
}

.message.remote {
    background: #f1f3f4;
    color: #333;
}

#chatInput {
    display: flex;
    gap: 10px;
}

#messageInput {
    flex: 1;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 20px;
    font-size: 14px;
}

#messageInput:focus {
    outline: none;
    border-color: #007bff;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    h1 {
        font-size: 2rem;
        margin-bottom: 20px;
    }
    
    #videos {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }
    
    video {
        width: 280px;
        height: 210px;
    }
    
    #buttons, #controls {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 200px;
        margin-bottom: 10px;
    }
    
    .modal-content {
        margin: 20px;
        min-width: auto;
        width: calc(100% - 40px);
    }
}

/* Loading animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Status indicators */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-connected {
    background: #28a745;
}

.status-connecting {
    background: #ffc107;
    animation: pulse 1s infinite;
}

.status-disconnected {
    background: #dc3545;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}
