name: <PERSON><PERSON> (<PERSON><PERSON>)
on:
  push:
    branches: [main]
permissions:
  contents: read
jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [macos-latest, ubuntu-latest, windows-latest]
        ver: ['3.8', '3.9', '3.10', '3.11', '3.12']
    env:
      BAZELISK_GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - uses: actions/checkout@v4.1.7
      - uses: bazel-contrib/setup-bazel@0.8.5
        with:
          bazelisk-version: '1.x'
      - uses: actions/setup-python@v5.1.0
        with:
          python-version: ${{ matrix.ver }}
      - name: Prepare Python ${{ matrix.ver }} environment
        run: |
          python -m pip install --upgrade pip
          python -m pip install --upgrade absl-py mypy
          python python/toolchains/generate.py
        shell: bash
      - run: .github/bazel.sh
        shell: bash
      # TODO(junyer): Run mypy as per https://github.com/google/re2/issues/496.
