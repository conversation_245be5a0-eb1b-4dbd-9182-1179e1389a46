name: <PERSON><PERSON> (CMake)
on:
  push:
    branches: [main]
permissions:
  contents: read
jobs:
  build-linux:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        build_shared_libs: [OFF, ON]
    steps:
      - uses: actions/checkout@v4.1.7
      - name: Install Abseil, GoogleTest and Benchmark
        run: |
          vcpkg update
          vcpkg install abseil gtest benchmark
        shell: bash
      - run: |
          .github/cmake.sh -D BUILD_SHARED_LIBS=${{ matrix.build_shared_libs }} \
            -D CMAKE_TOOLCHAIN_FILE=/usr/local/share/vcpkg/scripts/buildsystems/vcpkg.cmake
        shell: bash
  build-macos:
    runs-on: macos-latest
    strategy:
      fail-fast: false
      matrix:
        build_shared_libs: [OFF, ON]
    steps:
      - uses: actions/checkout@v4.1.7
      - name: Install Abseil, GoogleTest and Benchmark
        run: |
          brew update
          brew install abseil googletest google-benchmark
        shell: bash
      - run: .github/cmake.sh -D BUILD_SHARED_LIBS=${{ matrix.build_shared_libs }}
        shell: bash
  build-windows:
    runs-on: windows-latest
    strategy:
      fail-fast: false
      matrix:
        build_shared_libs: [OFF, ON]
    steps:
      - uses: actions/checkout@v4.1.7
      - name: Install Abseil, GoogleTest and Benchmark
        run: |
          vcpkg update
          vcpkg install abseil gtest benchmark
        shell: bash
      - run: |
          .github/cmake.sh -D BUILD_SHARED_LIBS=${{ matrix.build_shared_libs }} \
            -D CMAKE_TOOLCHAIN_FILE=C:/vcpkg/scripts/buildsystems/vcpkg.cmake
        shell: bash
