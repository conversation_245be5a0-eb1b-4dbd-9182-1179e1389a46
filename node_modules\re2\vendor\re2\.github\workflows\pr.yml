name: PR
on:
  pull_request_target:
    branches: [main]
    types: [opened]
permissions:
  contents: read
jobs:
  close:
    permissions:
      contents: read
      # We have to use two different APIs below,
      # so just grant two different permissions.
      issues: write
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4.1.7
      - uses: actions/github-script@v7.0.1
        with:
          script: |
            const fs = require('fs');
            console.log(await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              body: fs.readFileSync('CONTRIBUTING.md', { encoding: 'utf8', }),
            }));
            console.log(await github.rest.pulls.update({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: context.issue.number,
              state: 'closed',
            }));
