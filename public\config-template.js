// Firebase Configuration Template
// Copy this file to main.js and replace with your actual Firebase config

const firebaseConfig = {
    apiKey: "your-api-key-here",
    authDomain: "your-project-id.firebaseapp.com",
    projectId: "your-project-id",
    storageBucket: "your-project-id.appspot.com",
    messagingSenderId: "your-sender-id",
    appId: "your-app-id"
};

// Instructions:
// 1. Go to Firebase Console (https://console.firebase.google.com)
// 2. Select your project
// 3. Go to Project Settings (gear icon)
// 4. Scroll down to "Your apps" section
// 5. Click on your web app or create one
// 6. Copy the config object and replace the values above
// 7. Update the firebaseConfig object in main.js with these values

// Example of what your config might look like:
/*
const firebaseConfig = {
    apiKey: "AIzaSyDOCAbC123dEf456GhI789jKl01MnO2PqR",
    authDomain: "my-video-chat-12345.firebaseapp.com",
    projectId: "my-video-chat-12345",
    storageBucket: "my-video-chat-12345.appspot.com",
    messagingSenderId: "123456789012",
    appId: "1:123456789012:web:abcdef123456789012345"
};
*/
