# WebRTC Firebase Video Chat Application

A complete peer-to-peer video chat application built with WebRTC and Firebase Firestore for signaling. Features include video calling, audio/video controls, screen sharing, and real-time chat.

## Features

- 🎥 **Peer-to-peer video calling** using WebRTC
- 🔊 **Audio/Video controls** (mute, camera on/off)
- 🖥️ **Screen sharing** capability
- 💬 **Real-time chat** during video calls
- 📱 **Responsive design** for mobile and desktop
- 🔒 **Secure** Firebase Firestore signaling
- 🎨 **Modern UI** with smooth animations

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (LTS version recommended) - [Download here](https://nodejs.org/)
- **Firebase CLI** - Install with `npm install -g firebase-tools`
- **Git** - [Download here](https://git-scm.com/)

## Quick Start

### 1. Clone the Repository

```bash
git clone <your-repo-url>
cd videoCall
```

### 2. Install Dependencies

```bash
npm install -g firebase-tools
```

### 3. Set up Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Click "Add project" and create a new project
3. Enable **Cloud Firestore** in test mode
4. Go to Project Settings → General → Your apps
5. Click "Add app" and select Web (</>)
6. Copy your Firebase configuration

### 4. Configure Firebase

Replace the Firebase configuration in `public/main.js`:

```javascript
const firebaseConfig = {
    apiKey: "your-api-key-here",
    authDomain: "your-project-id.firebaseapp.com",
    projectId: "your-project-id",
    storageBucket: "your-project-id.appspot.com",
    messagingSenderId: "your-sender-id",
    appId: "your-app-id"
};
```

### 5. Initialize Firebase

```bash
firebase login
firebase use --add
# Select your project and give it an alias (e.g., 'default')
```

### 6. Deploy Firestore Rules

```bash
firebase deploy --only firestore:rules
```

### 7. Run the Application

```bash
npm start
# or
firebase serve --only hosting
```

Your app will be available at `http://localhost:5000`

## How to Use

### Starting a Video Call

1. **Start Camera**: Click "Start Camera" to access your camera and microphone
2. **Create Room**: Click "Create Room" to create a new video chat room
3. **Share Room ID**: Copy the Room ID and share it with the person you want to call
4. **Join Room**: The other person clicks "Join Room" and enters your Room ID

### During a Call

- **Mute/Unmute**: Toggle your microphone
- **Stop/Start Video**: Toggle your camera
- **Share Screen**: Share your screen with the other person
- **Chat**: Send text messages during the call
- **Hang Up**: End the call and clean up resources

## Project Structure

```
videoCall/
├── public/
│   ├── index.html          # Main HTML file
│   ├── style.css           # Styling and responsive design
│   └── main.js             # WebRTC and Firebase logic
├── firebase.json           # Firebase hosting configuration
├── firestore.rules         # Firestore security rules
├── firestore.indexes.json  # Firestore indexes
├── package.json            # Project dependencies and scripts
└── README.md              # This file
```

## Key Technologies

- **WebRTC**: Peer-to-peer real-time communication
- **Firebase Firestore**: Real-time database for signaling
- **Firebase Hosting**: Static web hosting
- **Vanilla JavaScript**: No frameworks, pure JS implementation
- **CSS3**: Modern styling with flexbox and animations

## Browser Compatibility

- ✅ Chrome (recommended)
- ✅ Firefox
- ✅ Safari (iOS 11+)
- ✅ Edge
- ⚠️ Requires HTTPS in production (localhost works for development)

## Deployment

### Deploy to Firebase Hosting

```bash
firebase deploy --only hosting
```

Your app will be live at `https://your-project-id.web.app`

### Deploy Everything

```bash
firebase deploy
```

This deploys hosting, Firestore rules, and indexes.

## Security Considerations

### For Production Use

1. **Update Firestore Rules**: The current rules allow all read/write access with time expiration
2. **Add Authentication**: Implement user authentication
3. **Use TURN Servers**: For users behind firewalls
4. **Rate Limiting**: Implement rate limiting for room creation
5. **Room Cleanup**: Add automatic room cleanup for old/unused rooms

### Example Production Firestore Rules

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /rooms/{roomId} {
      allow read, write: if request.auth != null 
        && request.time < timestamp.date(2025, 12, 31);
      
      match /{subcollection=**} {
        allow read, write: if request.auth != null;
      }
    }
  }
}
```

## Troubleshooting

### Common Issues

1. **Camera/Microphone Access Denied**
   - Ensure you're using HTTPS or localhost
   - Check browser permissions in settings
   - Try a different browser

2. **Connection Failed**
   - Verify Firebase configuration is correct
   - Check browser console for errors
   - Ensure Firestore rules are deployed

3. **No Remote Video**
   - Both users must grant camera/microphone permissions
   - Check network connectivity
   - Try refreshing both browser windows

### Debug Mode

Open browser developer tools (F12) to see detailed logs in the console.

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Based on the official [WebRTC Firebase Codelab](https://webrtc.org/getting-started/firebase-rtc-codelab)
- Uses Google's STUN servers for NAT traversal
- Firebase for real-time signaling infrastructure
