// Firebase Configuration - Replace with your actual config
const firebaseConfig = {
    apiKey: "your-api-key-here",
    authDomain: "your-project-id.firebaseapp.com",
    projectId: "your-project-id",
    storageBucket: "your-project-id.appspot.com",
    messagingSenderId: "your-sender-id",
    appId: "your-app-id"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);
const db = firebase.firestore();

// WebRTC Configuration
const configuration = {
    iceServers: [
        {
            urls: [
                'stun:stun1.l.google.com:19302',
                'stun:stun2.l.google.com:19302'
            ]
        }
    ],
    iceCandidatePoolSize: 10
};

// Global variables
let peerConnection = null;
let localStream = null;
let remoteStream = null;
let roomId = null;
let dataChannel = null;

// DOM elements
const localVideo = document.querySelector('#localVideo');
const remoteVideo = document.querySelector('#remoteVideo');
const startButton = document.querySelector('#startButton');
const createBtn = document.querySelector('#createBtn');
const joinBtn = document.querySelector('#joinBtn');
const hangupBtn = document.querySelector('#hangupBtn');
const currentRoomSpan = document.querySelector('#currentRoom');
const roomDialog = document.querySelector('#roomDialog');
const roomIdInput = document.querySelector('#roomId');
const confirmJoinBtn = document.querySelector('#confirmJoinBtn');
const cancelJoinBtn = document.querySelector('#cancelJoinBtn');

// Control elements
const muteBtn = document.querySelector('#muteBtn');
const videoBtn = document.querySelector('#videoBtn');
const shareScreenBtn = document.querySelector('#shareScreenBtn');
const controlsDiv = document.querySelector('#controls');

// Chat elements
const chatContainer = document.querySelector('#chatContainer');
const messagesDiv = document.querySelector('#messages');
const messageInput = document.querySelector('#messageInput');
const sendBtn = document.querySelector('#sendBtn');

// Initialize the application
function init() {
    startButton.addEventListener('click', openUserMedia);
    createBtn.addEventListener('click', createRoom);
    joinBtn.addEventListener('click', showJoinDialog);
    confirmJoinBtn.addEventListener('click', joinRoom);
    cancelJoinBtn.addEventListener('click', hideJoinDialog);
    hangupBtn.addEventListener('click', hangUp);
    
    // Control event listeners
    muteBtn.addEventListener('click', toggleMute);
    videoBtn.addEventListener('click', toggleVideo);
    shareScreenBtn.addEventListener('click', shareScreen);
    
    // Chat event listeners
    sendBtn.addEventListener('click', sendMessage);
    messageInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });
}

// Open user media (camera and microphone)
async function openUserMedia() {
    try {
        showLoading(startButton);
        
        const stream = await navigator.mediaDevices.getUserMedia({
            video: true,
            audio: true
        });
        
        localVideo.srcObject = stream;
        localStream = stream;
        remoteStream = new MediaStream();
        remoteVideo.srcObject = remoteStream;

        console.log('Stream:', stream);
        
        // Update UI
        startButton.disabled = true;
        createBtn.disabled = false;
        joinBtn.disabled = false;
        hangupBtn.disabled = false;
        controlsDiv.style.display = 'flex';
        
        hideLoading(startButton);
        startButton.textContent = 'Camera Started';
        
    } catch (error) {
        console.error('Error accessing media devices:', error);
        alert('Error accessing camera/microphone. Please ensure permissions are granted and try again.');
        hideLoading(startButton);
    }
}

// Create a new room
async function createRoom() {
    try {
        showLoading(createBtn);
        
        createBtn.disabled = true;
        joinBtn.disabled = true;
        
        // Create peer connection
        peerConnection = new RTCPeerConnection(configuration);
        registerPeerConnectionListeners();
        
        // Create data channel for chat
        dataChannel = peerConnection.createDataChannel('chat');
        setupDataChannel(dataChannel);
        
        // Add local stream tracks to peer connection
        localStream.getTracks().forEach(track => {
            peerConnection.addTrack(track, localStream);
        });

        // Create room document
        const roomRef = db.collection('rooms').doc();
        roomId = roomRef.id;
        
        const callerCandidatesCollection = roomRef.collection('callerCandidates');

        // Collect ICE candidates
        peerConnection.addEventListener('icecandidate', event => {
            if (!event.candidate) {
                console.log('Got final candidate!');
                return;
            }
            console.log('Got candidate: ', event.candidate);
            callerCandidatesCollection.add(event.candidate.toJSON());
        });

        // Create offer
        const offer = await peerConnection.createOffer();
        await peerConnection.setLocalDescription(offer);
        console.log('Created offer:', offer);

        const roomWithOffer = {
            'offer': {
                type: offer.type,
                sdp: offer.sdp
            },
            'createdAt': firebase.firestore.FieldValue.serverTimestamp()
        };

        await roomRef.set(roomWithOffer);
        console.log(`New room created with SDP offer. Room ID: ${roomId}`);
        
        updateRoomInfo(`Room ID: ${roomId} - You are the caller!`, 'connected');
        
        // Listen for remote session description
        roomRef.onSnapshot(async snapshot => {
            const data = snapshot.data();
            if (!peerConnection.currentRemoteDescription && data && data.answer) {
                console.log('Got remote description: ', data.answer);
                const rtcSessionDescription = new RTCSessionDescription(data.answer);
                await peerConnection.setRemoteDescription(rtcSessionDescription);
            }
        });

        // Listen for remote ICE candidates
        roomRef.collection('calleeCandidates').onSnapshot(snapshot => {
            snapshot.docChanges().forEach(async change => {
                if (change.type === 'added') {
                    let data = change.doc.data();
                    console.log(`Got new remote ICE candidate: ${JSON.stringify(data)}`);
                    await peerConnection.addIceCandidate(new RTCIceCandidate(data));
                }
            });
        });
        
        hideLoading(createBtn);
        chatContainer.style.display = 'block';
        
    } catch (error) {
        console.error('Error creating room:', error);
        alert('Error creating room. Please try again.');
        hideLoading(createBtn);
        createBtn.disabled = false;
        joinBtn.disabled = false;
    }
}

// Show join room dialog
function showJoinDialog() {
    roomDialog.style.display = 'flex';
    roomIdInput.focus();
}

// Hide join room dialog
function hideJoinDialog() {
    roomDialog.style.display = 'none';
    roomIdInput.value = '';
}

// Join an existing room
async function joinRoom() {
    const inputRoomId = roomIdInput.value.trim();
    if (!inputRoomId) {
        alert('Please enter a room ID');
        return;
    }
    
    try {
        showLoading(confirmJoinBtn);
        
        createBtn.disabled = true;
        joinBtn.disabled = true;
        roomId = inputRoomId;
        
        console.log('Join room: ', roomId);
        updateRoomInfo(`Room ID: ${roomId} - You are the callee!`, 'connecting');
        
        const roomRef = db.collection('rooms').doc(roomId);
        const roomSnapshot = await roomRef.get();
        
        if (!roomSnapshot.exists) {
            alert('Room not found. Please check the room ID.');
            hideLoading(confirmJoinBtn);
            createBtn.disabled = false;
            joinBtn.disabled = false;
            return;
        }
        
        console.log('Create PeerConnection with configuration: ', configuration);
        peerConnection = new RTCPeerConnection(configuration);
        registerPeerConnectionListeners();
        
        // Handle data channel
        peerConnection.addEventListener('datachannel', event => {
            const receiveChannel = event.channel;
            setupDataChannel(receiveChannel);
        });
        
        // Add local stream tracks
        localStream.getTracks().forEach(track => {
            peerConnection.addTrack(track, localStream);
        });

        // Collect ICE candidates
        const calleeCandidatesCollection = roomRef.collection('calleeCandidates');
        peerConnection.addEventListener('icecandidate', event => {
            if (!event.candidate) {
                console.log('Got final candidate!');
                return;
            }
            console.log('Got candidate: ', event.candidate);
            calleeCandidatesCollection.add(event.candidate.toJSON());
        });

        // Create SDP answer
        const offer = roomSnapshot.data().offer;
        console.log('Got offer:', offer);
        await peerConnection.setRemoteDescription(new RTCSessionDescription(offer));
        const answer = await peerConnection.createAnswer();
        console.log('Created answer:', answer);
        await peerConnection.setLocalDescription(answer);

        const roomWithAnswer = {
            answer: {
                type: answer.type,
                sdp: answer.sdp
            }
        };
        await roomRef.update(roomWithAnswer);

        // Listen for remote ICE candidates
        roomRef.collection('callerCandidates').onSnapshot(snapshot => {
            snapshot.docChanges().forEach(async change => {
                if (change.type === 'added') {
                    let data = change.doc.data();
                    console.log(`Got new remote ICE candidate: ${JSON.stringify(data)}`);
                    await peerConnection.addIceCandidate(new RTCIceCandidate(data));
                }
            });
        });
        
        hideJoinDialog();
        hideLoading(confirmJoinBtn);
        chatContainer.style.display = 'block';
        updateRoomInfo(`Room ID: ${roomId} - Connected!`, 'connected');
        
    } catch (error) {
        console.error('Error joining room:', error);
        alert('Error joining room. Please try again.');
        hideLoading(confirmJoinBtn);
        createBtn.disabled = false;
        joinBtn.disabled = false;
    }
}

// Hang up call
async function hangUp() {
    try {
        // Stop local tracks
        if (localStream) {
            localStream.getTracks().forEach(track => {
                track.stop();
            });
        }

        // Stop remote tracks
        if (remoteStream) {
            remoteStream.getTracks().forEach(track => track.stop());
        }

        // Close peer connection
        if (peerConnection) {
            peerConnection.close();
        }

        // Close data channel
        if (dataChannel) {
            dataChannel.close();
        }

        // Clear video elements
        localVideo.srcObject = null;
        remoteVideo.srcObject = null;

        // Reset UI
        startButton.disabled = false;
        startButton.textContent = 'Start Camera';
        createBtn.disabled = true;
        joinBtn.disabled = true;
        hangupBtn.disabled = true;
        controlsDiv.style.display = 'none';
        chatContainer.style.display = 'none';
        currentRoomSpan.textContent = '';

        // Delete room on hangup
        if (roomId) {
            const roomRef = db.collection('rooms').doc(roomId);

            // Delete subcollections
            const calleeCandidates = await roomRef.collection('calleeCandidates').get();
            calleeCandidates.forEach(async candidate => {
                await candidate.ref.delete();
            });

            const callerCandidates = await roomRef.collection('callerCandidates').get();
            callerCandidates.forEach(async candidate => {
                await candidate.ref.delete();
            });

            await roomRef.delete();
        }

        // Reset variables
        peerConnection = null;
        localStream = null;
        remoteStream = null;
        roomId = null;
        dataChannel = null;

        console.log('Hung up successfully');

    } catch (error) {
        console.error('Error during hangup:', error);
    }
}

// Register peer connection listeners
function registerPeerConnectionListeners() {
    peerConnection.addEventListener('icegatheringstatechange', () => {
        console.log(`ICE gathering state changed: ${peerConnection.iceGatheringState}`);
    });

    peerConnection.addEventListener('connectionstatechange', () => {
        console.log(`Connection state change: ${peerConnection.connectionState}`);
        updateConnectionStatus(peerConnection.connectionState);
    });

    peerConnection.addEventListener('signalingstatechange', () => {
        console.log(`Signaling state change: ${peerConnection.signalingState}`);
    });

    peerConnection.addEventListener('iceconnectionstatechange', () => {
        console.log(`ICE connection state change: ${peerConnection.iceConnectionState}`);
    });

    peerConnection.addEventListener('track', event => {
        console.log('Got remote track:', event.streams[0]);
        event.streams[0].getTracks().forEach(track => {
            console.log('Add a track to the remoteStream:', track);
            remoteStream.addTrack(track);
        });
    });
}

// Media controls
function toggleMute() {
    if (localStream) {
        const audioTrack = localStream.getAudioTracks()[0];
        if (audioTrack) {
            audioTrack.enabled = !audioTrack.enabled;
            muteBtn.textContent = audioTrack.enabled ? 'Mute' : 'Unmute';
            muteBtn.className = audioTrack.enabled ? 'btn btn-warning' : 'btn btn-danger';
        }
    }
}

function toggleVideo() {
    if (localStream) {
        const videoTrack = localStream.getVideoTracks()[0];
        if (videoTrack) {
            videoTrack.enabled = !videoTrack.enabled;
            videoBtn.textContent = videoTrack.enabled ? 'Stop Video' : 'Start Video';
            videoBtn.className = videoTrack.enabled ? 'btn btn-warning' : 'btn btn-danger';
        }
    }
}

// Screen sharing
async function shareScreen() {
    try {
        const screenStream = await navigator.mediaDevices.getDisplayMedia({
            video: true,
            audio: true
        });

        const videoTrack = screenStream.getVideoTracks()[0];
        const sender = peerConnection.getSenders().find(s =>
            s.track && s.track.kind === 'video'
        );

        if (sender) {
            await sender.replaceTrack(videoTrack);
        }

        shareScreenBtn.textContent = 'Stop Sharing';
        shareScreenBtn.className = 'btn btn-danger';

        videoTrack.onended = async () => {
            // Switch back to camera when screen sharing ends
            const cameraStream = localStream.getVideoTracks()[0];
            if (sender && cameraStream) {
                await sender.replaceTrack(cameraStream);
            }
            shareScreenBtn.textContent = 'Share Screen';
            shareScreenBtn.className = 'btn btn-info';
        };

    } catch (error) {
        console.error('Error sharing screen:', error);
        alert('Error sharing screen. Please try again.');
    }
}

// Data channel setup for chat
function setupDataChannel(channel) {
    dataChannel = channel;

    dataChannel.onopen = () => {
        console.log('Data channel opened');
    };

    dataChannel.onclose = () => {
        console.log('Data channel closed');
    };

    dataChannel.onmessage = (event) => {
        console.log('Received message:', event.data);
        displayMessage(event.data, 'remote');
    };

    dataChannel.onerror = (error) => {
        console.error('Data channel error:', error);
    };
}

// Chat functionality
function sendMessage() {
    const message = messageInput.value.trim();
    if (message && dataChannel && dataChannel.readyState === 'open') {
        dataChannel.send(message);
        displayMessage(message, 'local');
        messageInput.value = '';
    } else if (!dataChannel || dataChannel.readyState !== 'open') {
        alert('Chat is not available. Please ensure you are connected to a peer.');
    }
}

function displayMessage(message, sender) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}`;
    messageDiv.textContent = message;
    messagesDiv.appendChild(messageDiv);
    messagesDiv.scrollTop = messagesDiv.scrollHeight;
}

// UI helper functions
function updateRoomInfo(text, status) {
    currentRoomSpan.innerHTML = `<span class="status-indicator status-${status}"></span>${text}`;
}

function updateConnectionStatus(state) {
    const statusMap = {
        'connecting': 'connecting',
        'connected': 'connected',
        'disconnected': 'disconnected',
        'failed': 'disconnected',
        'closed': 'disconnected'
    };

    const status = statusMap[state] || 'disconnected';
    const indicator = document.querySelector('.status-indicator');
    if (indicator) {
        indicator.className = `status-indicator status-${status}`;
    }
}

function showLoading(button) {
    button.innerHTML = '<span class="loading"></span> Loading...';
    button.disabled = true;
}

function hideLoading(button) {
    button.disabled = false;
}

// Error handling
window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
});

// Initialize when page loads
document.addEventListener('DOMContentLoaded', init);

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (peerConnection) {
        hangUp();
    }
});
